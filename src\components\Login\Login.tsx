import React, { useEffect } from 'react';
import Navigation from '../Navigation';
import Footer from '../Footer';
import styles from './Login.module.scss';
import useAuth from '../Auth/AuthActions';
import type { NotificationsProps } from '../Notifications/Notification';
import { useNavigate } from 'react-router-dom';
import type { AuthStatusProps } from '../Auth/AuthTypes';

interface LoginProps extends NotificationsProps, AuthStatusProps {

}

const Login: React.FC<LoginProps> = ({ setNotifications, notifications, setIsLoggedIn, isLoggedIn }) => {
    const { login, getBearerToken } = useAuth({ setNotifications, notifications, setIsLoggedIn, isLoggedIn });
    let navigate = useNavigate();

    useEffect(() => {
        let token = getBearerToken();
        if (token) {
            setIsLoggedIn?.(true);
        }
    }, [])

    useEffect(() => {
        if (isLoggedIn) {
            navigate('/account');
            return;
        }
    }, [isLoggedIn])

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const email = formData.get('email') as string;
        const password = formData.get('password') as string;
        console.log('Logging in with:', { email, password });
        await login(email, password).then((res) => {
            if (res) {
                setIsLoggedIn?.(true);
                navigate('/account');
            }
        });
    };

    return (
        <div className={styles.loginPage}>
            <Navigation />

            <main className={styles.mainContent}>
                <section className={styles.formSection}>
                    <h1 className={styles.title}>Login</h1>
                    <form className={styles.loginForm} onSubmit={handleSubmit}>
                        <label htmlFor="email">
                            Email
                            <input
                                type="email"
                                id="email"
                                name="email"
                                required
                                className={styles.input}
                            />
                        </label>
                        <label htmlFor="password">
                            Password
                            <input
                                type="password"
                                id="password"
                                name="password"
                                required
                                className={styles.input}
                            />
                        </label>
                        <button type="submit" className={styles.submitBtn}>
                            Login
                        </button>
                        <button type="submit" className={styles.registerBtn} onClick={() => navigate('/register')}>
                            Create new account
                        </button>
                    </form>
                </section>
            </main>

            <Footer />
        </div>
    );
};

export default Login;
