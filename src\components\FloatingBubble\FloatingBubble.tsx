import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { FiLogIn, FiShoppingCart, FiUser } from "react-icons/fi";
import styles from "./FloatingBubble.module.scss";
import { TfiClose, TfiMenu } from "react-icons/tfi";
import useAuth from "../Auth/AuthActions";
import type { NotificationsProps } from "../Notifications/Notification";
import type { AuthStatusProps } from "../Auth/AuthTypes";

interface FloatingBubbleProps extends NotificationsProps, AuthStatusProps {}

const FloatingBubble: React.FC<FloatingBubbleProps> = ({
  notifications,
  setNotifications,
  isLoggedIn,
  setIsLoggedIn,
}) => {
  const [open, setOpen] = useState(false);
  const { getBearerToken, logout } = useAuth({
    setNotifications,
    notifications,
    setIsLoggedIn,
  });
  const [options, setOptions] = useState<any[]>([]);

  useEffect(() => {
    logout();
    console.log("test");
    var token = getBearerToken();
    console.log(token);
    if (getBearerToken()) {
      setOptions([
        { icon: <FiUser size={20} />, path: "/account" },
        { icon: <FiShoppingCart size={20} />, path: "/cart" },
      ]);
    } else {
      setOptions([
        { icon: <FiLogIn size={20} />, path: "/login" },
        { icon: <FiShoppingCart size={20} />, path: "/cart" },
      ]);
    }
  }, [isLoggedIn]);

  return (
    <div className={styles.container}>
      <div
        className={`${styles.options} ${open ? styles.open : styles.closed}`}
      >
        {options.map((opt) => (
          <Link
            key={opt.path}
            to={opt.path}
            className={styles.option}
            onClick={() => setOpen(false)}
          >
            {opt.icon}
          </Link>
        ))}
      </div>
      <button
        type="button"
        className={styles.bubble}
        onClick={() => setOpen((prev) => !prev)}
        aria-label={open ? "Close menu" : "Open menu"}
      >
        {open ? <TfiClose size={32} /> : <TfiMenu size={32} />}
      </button>
    </div>
  );
};

export default FloatingBubble;
