# Token Refresh Implementation

## Overview
Enhanced the `getBearerToken` function in `AuthActions.tsx` to automatically check token expiration and refresh tokens when needed.

## Changes Made

### 1. Enhanced `getBearerToken` Function
- **Before**: Simple function that returned stored access token
- **After**: Async function that checks token expiration and refreshes if needed

### 2. New Helper Functions Added

#### `decodeJWTPayload(token: string)`
- Decodes JWT token payload without validation
- Converts base64url to base64 and parses JSON
- Returns payload object or null if invalid

#### `isTokenExpired(token: string)`
- Checks if JWT token is expired
- <PERSON><PERSON>ares token's `exp` claim with current time
- Returns true if expired or invalid

#### `refreshAccessToken()`
- Calls `/TokenWeb/Refresh` endpoint with refresh token
- Updates localStorage with new tokens
- Clears tokens and logs out user if refresh fails

### 3. Updated Function Signature
```typescript
// Before
const getBearerToken = () => {
    return localStorage.getItem('AccessToken');
};

// After
const getBearerToken = async () => {
    const accessToken = localStorage.getItem('AccessToken');
    
    if (!accessToken) {
        return null;
    }

    // Check if token is expired
    if (isTokenExpired(accessToken)) {
        try {
            // Try to refresh the token
            const newToken = await refreshAccessToken();
            return newToken;
        } catch (error) {
            console.error('Token refresh failed:', error);
            setNotifications([...notifications, { 
                id: Date.now(), 
                message: 'Session expired. Please log in again.' 
            }]);
            return null;
        }
    }

    return accessToken;
};
```

### 4. Updated All Usage Points
Updated all components that use `getBearerToken()` to handle async nature:

- **AccountPage.tsx**: Added async/await in useEffect
- **Register.tsx**: Added async/await in useEffect  
- **Login.tsx**: Added async/await in useEffect
- **FloatingBubble.tsx**: Added async/await in useEffect
- **AddressesActions.tsx**: Moved token retrieval inside async function

## API Endpoint Assumption
The implementation assumes a refresh endpoint at:
```
POST /TokenWeb/Refresh
Content-Type: application/json
Body: { "refreshToken": "..." }
```

Response should include:
```json
{
    "AccessToken": "new_access_token",
    "RefreshToken": "new_refresh_token" // optional
}
```

## Error Handling
- If refresh token is missing: throws error
- If refresh API fails: clears tokens, logs out user, shows notification
- If token decoding fails: treats as expired
- Graceful fallback to login flow

## Benefits
1. **Automatic token refresh**: Users don't need to manually re-login
2. **Seamless UX**: Token refresh happens transparently
3. **Security**: Expired tokens are automatically handled
4. **Error resilience**: Graceful fallback to login on failures

## Testing
A test utility was created at `src/utils/tokenTest.ts` to verify token decoding logic.

## Notes
- All existing functionality remains intact
- Backward compatible with existing API calls
- Refresh endpoint URL may need adjustment based on actual backend implementation
