import React from "react";
import styles from "../AccountPage.module.scss";

const OrdersTab: React.FC = () => {
  return (
    <div className={styles.tabContent}>
      <h2>Account Settings</h2>
      <div className={styles.settingsSection}>
        <div className={styles.settingItem}>
          <label className={styles.switchLabel}>
            <input type="checkbox" />
            <span className={styles.switch}></span>
            Email Notifications
          </label>
        </div>
        <div className={styles.settingItem}>
          <label className={styles.switchLabel}>
            <input type="checkbox" />
            <span className={styles.switch}></span>
            SMS Notifications
          </label>
        </div>
        <div className={styles.settingItem}>
          <label className={styles.switchLabel}>
            <input type="checkbox" />
            <span className={styles.switch}></span>
            Marketing Communications
          </label>
        </div>
        <div className={styles.formGroup}>
          <label htmlFor="language">Preferred Language</label>
          <select id="language">
            <option value="en">English</option>
            <option value="pl">Polish</option>
            <option value="de">German</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default OrdersTab;
