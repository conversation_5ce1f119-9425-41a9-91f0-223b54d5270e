import { UMBRACO_URL } from "../../env-config";
import type { NotificationsProps } from "../Notifications/Notification";
import type { AuthStatusProps } from "./AuthTypes";

interface AuthProps extends NotificationsProps, AuthStatusProps {
    
}

const useAuth = ({ setNotifications, notifications, setIsLoggedIn }: AuthProps) => {
    const getBearerToken = () => {
        return localStorage.getItem('AccessToken');
    };

    const getRefreshToken = () => {
        return localStorage.getItem('RefreshToken');
    };

    const login = async (email: string, password: string) => {
        try {
            const response = await fetch(`${UMBRACO_URL}/TokenWeb/Login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, password }),
            });
            if (!response.ok) {
                const errorData = await response.json();
                setNotifications([...notifications, { id: Date.now(), message: `${errorData.message || 'Login failed'}` }]);
                throw new Error(errorData.message || 'Login failed');
            }
            const data = await response.json();
            console.log(data);
            localStorage.setItem('AccessToken', data.AccessToken);
            localStorage.setItem('RefreshToken', data.RefreshToken);
            setNotifications([...notifications, { id: Date.now(), message: `${'Login successful'}` }]);
            setIsLoggedIn?.(true);

            return true;
        } catch (error) {
            setNotifications([...notifications, { id: Date.now(), message: `${error}` }]);
            throw error;
        }
    };

    const getAntiforgeryToken = async () => {
        try {
            const response = await fetch(`${UMBRACO_URL}/TokenWeb/Antiforgery`, {
                method: 'GET',
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Antiforgery token retrieval failed');
            }
            const data = await response.text();
            return data;
        } catch (error) {
            setNotifications([...notifications, { id: Date.now(), message: `${error}` }]);
            throw error;
        }
    };

    const register = async (email: string, password: string, firstName: string, secondName: string) => {
        try {
            const antiforgeryToken = await getAntiforgeryToken();
            const response = await fetch(`${UMBRACO_URL}/Account/Register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': antiforgeryToken,
                    'returnUrl': '/'
                },
                body: JSON.stringify({ email, password, firstName, secondName }),
            });
            if (!response.ok) {
                const errorData = await response.json();
                setNotifications([...notifications, { id: Date.now(), message: `${errorData.message || 'Registration failed'}` }]);
                throw new Error(errorData.message || 'Registration failed');
            }
            const data = await response.json();
            console.log(data);
            //localStorage.setItem('token', data.token);
            setNotifications([...notifications, { id: Date.now(), message: `${'Registration successful'}` }]);

            return true;
        } catch (error) {
            setNotifications([...notifications, { id: Date.now(), message: `${error}` }]);
            throw error;
        }
    };

    const logout = () => {
        localStorage.removeItem('AccessToken');
        localStorage.removeItem('RefreshToken');
        setIsLoggedIn?.(false);
    }

    return { getBearerToken, getRefreshToken, login, register, logout };
};

export default useAuth;
