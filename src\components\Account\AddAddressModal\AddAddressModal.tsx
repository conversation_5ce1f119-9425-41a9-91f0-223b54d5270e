import React, { useState } from "react";
import Modal from "../../Modal";
import styles from "./AddAddressModal.module.scss";
import { UMBRACO_URL } from "../../../env-config";
import useAuth from "../../Auth/AuthActions";
import type { NotificationsProps } from "../../Notifications/Notification";

interface AddAddressModalProps extends NotificationsProps {
  isOpen: boolean;
  onClose: () => void;
  onAddressAdded: () => void;
}

interface AddressFormData {
  FirstName: string;
  LastName: string;
  Email: string;
  Company: string;
  CountryId: string;
  StateProvinceId: string;
  City: string;
  Address1: string;
  Address2: string;
  ZipPostalCode: string;
  PhoneNumber: string;
}

const AddAddressModal: React.FC<AddAddressModalProps> = ({
  isOpen,
  onClose,
  onAddressAdded,
  notifications,
  setNotifications,
}) => {
  const { getBearerToken, getAntiforgeryToken } = useAuth({
    notifications,
    setNotifications,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<AddressFormData>({
    FirstName: "",
    LastName: "",
    Email: "",
    Company: "",
    CountryId: "67b59c23353179806f19707f", // Default country ID from example
    StateProvinceId: "67b59c23353179806f19708f", // Default state ID from example
    City: "",
    Address1: "",
    Address2: "",
    ZipPostalCode: "",
    PhoneNumber: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const token = await getBearerToken();
      const antiforgeryToken = await getAntiforgeryToken();

      if (!token) {
        throw new Error("Authentication required");
      }

      // Create form data as URL-encoded string to match the example request
      const formBody = new URLSearchParams();
      formBody.append("Address.Id", "");
      formBody.append("Address.FirstName", formData.FirstName);
      formBody.append("Address.LastName", formData.LastName);
      formBody.append("Address.Email", formData.Email);
      formBody.append("Address.Company", formData.Company);
      formBody.append("Address.CountryId", formData.CountryId);
      formBody.append("Address.StateProvinceId", formData.StateProvinceId);
      formBody.append("Address.City", formData.City);
      formBody.append("Address.Address1", formData.Address1);
      formBody.append("Address.Address2", formData.Address2);
      formBody.append("Address.ZipPostalCode", formData.ZipPostalCode);
      formBody.append("Address.PhoneNumber", formData.PhoneNumber);
      formBody.append("__RequestVerificationToken", antiforgeryToken);

      const response = await fetch(`${UMBRACO_URL}/account/addressadd`, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: `Bearer ${token}`,
        },
        body: formBody.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to add address: ${errorText}`);
      }

      setNotifications([
        ...notifications,
        { id: Date.now(), message: "Address added successfully!" },
      ]);

      onAddressAdded();
      onClose();

      // Reset form
      setFormData({
        FirstName: "",
        LastName: "",
        Email: "",
        Company: "",
        CountryId: "67b59c23353179806f19707f",
        StateProvinceId: "67b59c23353179806f19708f",
        City: "",
        Address1: "",
        Address2: "",
        ZipPostalCode: "",
        PhoneNumber: "",
      });
    } catch (error) {
      console.error("Error adding address:", error);
      setNotifications([
        ...notifications,
        { id: Date.now(), message: `Error adding address: ${error}` },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Add New Address">
      <form onSubmit={handleSubmit} className={styles.addressForm}>
        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="FirstName">First Name *</label>
            <input
              type="text"
              id="FirstName"
              name="FirstName"
              value={formData.FirstName}
              onChange={handleInputChange}
              required
              disabled={isLoading}
            />
          </div>
          <div className={styles.formGroup}>
            <label htmlFor="LastName">Last Name *</label>
            <input
              type="text"
              id="LastName"
              name="LastName"
              value={formData.LastName}
              onChange={handleInputChange}
              required
              disabled={isLoading}
            />
          </div>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="Email">Email *</label>
          <input
            type="email"
            id="Email"
            name="Email"
            value={formData.Email}
            onChange={handleInputChange}
            required
            disabled={isLoading}
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="Company">Company</label>
          <input
            type="text"
            id="Company"
            name="Company"
            value={formData.Company}
            onChange={handleInputChange}
            disabled={isLoading}
          />
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="City">City *</label>
            <input
              type="text"
              id="City"
              name="City"
              value={formData.City}
              onChange={handleInputChange}
              required
              disabled={isLoading}
            />
          </div>
          <div className={styles.formGroup}>
            <label htmlFor="ZipPostalCode">Postal Code *</label>
            <input
              type="text"
              id="ZipPostalCode"
              name="ZipPostalCode"
              value={formData.ZipPostalCode}
              onChange={handleInputChange}
              required
              disabled={isLoading}
            />
          </div>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="Address1">Address Line 1 *</label>
          <input
            type="text"
            id="Address1"
            name="Address1"
            value={formData.Address1}
            onChange={handleInputChange}
            required
            disabled={isLoading}
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="Address2">Address Line 2</label>
          <input
            type="text"
            id="Address2"
            name="Address2"
            value={formData.Address2}
            onChange={handleInputChange}
            disabled={isLoading}
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="PhoneNumber">Phone Number *</label>
          <input
            type="tel"
            id="PhoneNumber"
            name="PhoneNumber"
            value={formData.PhoneNumber}
            onChange={handleInputChange}
            required
            disabled={isLoading}
          />
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={handleClose}
            className={styles.cancelButton}
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.saveButton}
            disabled={isLoading}
          >
            {isLoading ? "Saving..." : "Save Address"}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default AddAddressModal;
