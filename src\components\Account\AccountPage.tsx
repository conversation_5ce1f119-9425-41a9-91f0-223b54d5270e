import React, { useEffect, useState } from "react";
import styles from "./AccountPage.module.scss";
import useAuth from "../Auth/AuthActions";
import type { NotificationsProps } from "../Notifications/Notification";
import { useNavigate } from "react-router-dom";
import type { AuthStatusProps } from "../Auth/AuthTypes";
import Navigation from "../Navigation";
import Footer from "../Footer";
import useAddresses from "../Addresses/AdressesActions";
import type AddressItem from "../Addresses/AddressItem";
import ProfileTab from "./ProfileTab";
import OrdersTab from "./OrdersTab";
import PasswordTab from "./PasswordTab";

interface AccountPageProps extends NotificationsProps, AuthStatusProps {}

type TabType = "profile" | "orders" | "password";

const AccountPage: React.FC<AccountPageProps> = ({
  notifications,
  setNotifications,
  setIsLoggedIn,
  isLoggedIn,
}) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<TabType>("profile");
  const { getBearerToken, getRefreshToken, logout } = useAuth({
    setNotifications,
    notifications,
    setIsLoggedIn,
    isLoggedIn,
  });
  const { getAddresses } = useAddresses({
    setNotifications,
    notifications,
    setIsLoggedIn,
    isLoggedIn,
  });
  const [addresses, setAddresses] = useState<AddressItem[]>([]);

  const handleLogout = async () => {
    await logout();
    navigate("/login");
  };

  useEffect(() => {
    const fetchData = async () => {
      const token = await getBearerToken();
      if (token) {
        setIsLoggedIn?.(true);
        const addresses = await getAddresses();
        setAddresses(addresses);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    if (!isLoggedIn) {
      navigate("/login");
      return;
    }
  }, [isLoggedIn]);

  const renderTabContent = () => {
    switch (activeTab) {
      case "profile":
        return <ProfileTab addresses={addresses} />;
      case "orders":
        return <OrdersTab />;
      case "password":
        return (
          <PasswordTab
            getBearerToken={getBearerToken}
            getRefreshToken={getRefreshToken}
            handleLogout={handleLogout}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Navigation />
      <div className={styles.account}>
        <div className={styles.accountContainer}>
          <div className={styles.sidebar}>
            <h1>My account</h1>
            <nav className={styles.tabNav}>
              <button
                className={`${styles.tabBtn} ${
                  activeTab === "profile" ? styles.active : ""
                }`}
                onClick={() => setActiveTab("profile")}
              >
                <span className={styles.tabIcon}>👤</span>
                Customer info
              </button>
              <button
                className={`${styles.tabBtn} ${
                  activeTab === "orders" ? styles.active : ""
                }`}
                onClick={() => setActiveTab("orders")}
              >
                <span className={styles.tabIcon}>📦</span>
                Orders
              </button>
              <button
                className={`${styles.tabBtn} ${
                  activeTab === "password" ? styles.active : ""
                }`}
                onClick={() => setActiveTab("password")}
              >
                <span className={styles.tabIcon}>🔒</span>
                Change password
              </button>
            </nav>
          </div>
          <div className={styles.mainContent}>{renderTabContent()}</div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default AccountPage;
