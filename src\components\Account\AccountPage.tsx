import React, { useEffect, useState } from "react";
import styles from "./AccountPage.module.scss";
import useAuth from "../Auth/AuthActions";
import type { NotificationsProps } from "../Notifications/Notification";
import { useNavigate } from "react-router-dom";
import type { AuthStatusProps } from "../Auth/AuthTypes";
import Navigation from "../Navigation";
import Footer from "../Footer";
import useAddresses from "../Addresses/AdressesActions";
import type AddressItem from "../Addresses/AddressItem";

interface AccountPageProps extends NotificationsProps, AuthStatusProps {}

type TabType = "profile" | "settings" | "security";

const AccountPage: React.FC<AccountPageProps> = ({
  notifications,
  setNotifications,
  setIsLoggedIn,
  isLoggedIn,
}) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<TabType>("profile");
  const { getBearerToken, getRefreshToken, logout } = useAuth({
    setNotifications,
    notifications,
    setIsLoggedIn,
    isLoggedIn,
  });
  const { getAddresses } = useAddresses({
    setNotifications,
    notifications,
    setIsLoggedIn,
    isLoggedIn,
  });
  const [addresses, setAddresses] = useState<AddressItem[]>([]);

  const handleLogout = async () => {
    await logout();
    navigate("/login");
  };

  useEffect(() => {
    const fetchData = async () => {
      const token = getBearerToken();
      if (token) {
        setIsLoggedIn?.(true);
        const addresses = await getAddresses();
        setAddresses(addresses);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    if (!isLoggedIn) {
      navigate("/login");
      return;
    }
  }, [isLoggedIn]);

  const renderTabContent = () => {
    switch (activeTab) {
      case "profile":
        return (
          <div className={styles.tabContent}>
            <h2>Profile Information</h2>
            <div className={styles.profileSection}>
              <div className={styles.nameSection}>
                <div className={styles.formGroup}>
                  <label htmlFor="firstName">First Name</label>
                  <input
                    type="text"
                    id="firstName"
                    placeholder="Enter your first name"
                  />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="lastName">Last Name</label>
                  <input
                    type="text"
                    id="lastName"
                    placeholder="Enter your last name"
                  />
                </div>
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="email">Email</label>
                <input type="email" id="email" readOnly />
              </div>
            </div>
            <h2> Addresses</h2>
            <div className={styles.profileSection}>
              <button className={styles.addressActionButton}>+ Add new</button>
              {addresses.length === 0 ? (
                <div className={styles.noAdreesses}>No addresses</div>
              ) : (
                <div>
                  {addresses.map((address) => (
                    <div key={address.Id} className={styles.addressItem}>
                      <div className={styles.addressName}>
                        <h3>
                          {address.FirstName} {address.LastName}
                        </h3>
                        {address.Email}
                      </div>
                      <div className={styles.addressDetails}>
                        <p>{address.Address1}</p>
                        <p>
                          {address.StateProvinceName}, {address.ZipPostalCode}{" "}
                          {address.City}
                        </p>
                        <p>{address.CountryName}</p>
                        <p>
                          <strong>Phone number: </strong>
                          {address.PhoneNumber}
                        </p>
                      </div>
                      <div className={styles.addressActions}>
                        <button className={styles.addressActionButton}>
                          Edit
                        </button>
                        <button className={styles.addressActionButton}>
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              <button className={styles.saveBtn}>Save Changes</button>
            </div>
          </div>
        );
      case "settings":
        return (
          <div className={styles.tabContent}>
            <h2>Account Settings</h2>
            <div className={styles.settingsSection}>
              <div className={styles.settingItem}>
                <label className={styles.switchLabel}>
                  <input type="checkbox" />
                  <span className={styles.switch}></span>
                  Email Notifications
                </label>
              </div>
              <div className={styles.settingItem}>
                <label className={styles.switchLabel}>
                  <input type="checkbox" />
                  <span className={styles.switch}></span>
                  SMS Notifications
                </label>
              </div>
              <div className={styles.settingItem}>
                <label className={styles.switchLabel}>
                  <input type="checkbox" />
                  <span className={styles.switch}></span>
                  Marketing Communications
                </label>
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="language">Preferred Language</label>
                <select id="language">
                  <option value="en">English</option>
                  <option value="pl">Polish</option>
                  <option value="de">German</option>
                </select>
              </div>
            </div>
          </div>
        );
      case "security":
        return (
          <div className={styles.tabContent}>
            <h2>Security Settings</h2>
            <div className={styles.securitySection}>
              <div className={styles.tokenInfo}>
                <p>
                  <strong>Access Token:</strong>{" "}
                  <code>{getBearerToken() || "Not logged in"}</code>
                </p>
                <p>
                  <strong>Refresh Token:</strong>{" "}
                  <code>{getRefreshToken() || "Not available"}</code>
                </p>
              </div>
              <div className={styles.passwordSection}>
                <h3>Change Password</h3>
                <div className={styles.formGroup}>
                  <label htmlFor="currentPassword">Current Password</label>
                  <input
                    type="password"
                    id="currentPassword"
                    placeholder="Enter current password"
                  />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="newPassword">New Password</label>
                  <input
                    type="password"
                    id="newPassword"
                    placeholder="Enter new password"
                  />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="confirmPassword">Confirm New Password</label>
                  <input
                    type="password"
                    id="confirmPassword"
                    placeholder="Confirm new password"
                  />
                </div>
                <button className={styles.saveBtn}>Update Password</button>
              </div>
              <div className={styles.dangerZone}>
                <h3>Danger Zone</h3>
                <button className={styles.logoutBtn} onClick={handleLogout}>
                  Logout
                </button>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Navigation />
      <div className={styles.account}>
        <div className={styles.accountContainer}>
          <div className={styles.sidebar}>
            <h1>My account</h1>
            <nav className={styles.tabNav}>
              <button
                className={`${styles.tabBtn} ${
                  activeTab === "profile" ? styles.active : ""
                }`}
                onClick={() => setActiveTab("profile")}
              >
                <span className={styles.tabIcon}>👤</span>
                Customer info
              </button>
              <button
                className={`${styles.tabBtn} ${
                  activeTab === "settings" ? styles.active : ""
                }`}
                onClick={() => setActiveTab("settings")}
              >
                <span className={styles.tabIcon}>📦</span>
                Orders
              </button>
              <button
                className={`${styles.tabBtn} ${
                  activeTab === "security" ? styles.active : ""
                }`}
                onClick={() => setActiveTab("security")}
              >
                <span className={styles.tabIcon}>🔒</span>
                Change password
              </button>
            </nav>
          </div>
          <div className={styles.mainContent}>{renderTabContent()}</div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default AccountPage;
