@import "../../styles/_variables.scss";

.account {
  padding: $spacing-lg;
  width: 100%;
  margin: $spacing-2xl auto;
  background: $color-bg-white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
}

.accountContainer {
  display: flex;
  gap: $spacing-xl;
  min-height: 600px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: $spacing-lg;
  }
}

.sidebar {
  flex: 1;
  // background: $color-bg-light;
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  height: fit-content;

  @media (max-width: 768px) {
    flex: none;
  }

  h1 {
    font-family: $font-primary;
    font-size: $font-size-5xl;
    margin-bottom: $spacing-lg;
    color: $color-text-primary;
    text-align: center;

    @media (max-width: 768px) {
      font-size: $font-size-4xl;
      margin-bottom: $spacing-md;
    }
  }
}

.tabNav {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;

  @media (max-width: 768px) {
    flex-direction: row;
    justify-content: space-around;
    gap: $spacing-sm;
  }
}

.tabBtn {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm;
  background: transparent;
  border: solid 1px $color-border-light;
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: $color-text-secondary;
  cursor: pointer;
  transition: all $transition-fast;
  text-align: left;
  width: 100%;

  &.active {
    border: solid 2px $color-primary;
    color: $color-primary;
    font-weight: $font-weight-semibold;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    padding: $spacing-sm;
    font-size: $font-size-sm;
    width: auto;
    flex: 1;
  }

  &:hover {
    background: rgba($color-primary, 0.1);
    color: $color-text-primary;
  }
}

.tabIcon {
  font-size: $font-size-lg;

  @media (max-width: 768px) {
    font-size: $font-size-xl;
  }
}

.mainContent {
  flex: 3;
  padding: $spacing-lg;
}

.tabContent {
  h2 {
    font-family: $font-primary;
    font-size: $font-size-4xl;
    margin-bottom: $spacing-lg;
    color: $color-text-primary;
    border-bottom: 2px solid $color-primary;
    padding-bottom: $spacing-sm;
  }

  h3 {
    font-family: $font-primary;
    font-size: $font-size-2xl;
    margin-bottom: $spacing-md;
    color: $color-text-primary;
  }
}

.formGroup {
  margin-bottom: $spacing-lg;
  flex: 1;

  label {
    display: block;
    margin-bottom: $spacing-xs;
    font-family: $font-secondary;
    font-weight: $font-weight-medium;
    color: $color-text-dark;
    font-size: $font-size-sm;
  }

  input,
  select {
    width: 100%;
    padding: $spacing-sm $spacing-md;
    border: 1px solid rgba($color-text-muted, 0.3);
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-size: $font-size-base;
    background: $color-bg-white;
    transition: border-color $transition-fast;

    &:focus {
      outline: none;
      border-color: $color-primary;
      box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
    }

    &::placeholder {
      color: $color-text-muted;
    }
  }
}

.profileSection {
  display: flex;
  flex-direction: column;
  width: 100%;

  .nameSection {
    display: flex;
    gap: $spacing-md;
    width: 100%;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0px;
    }
  }

  .noAdreesses {
    width: 100%;
    font-family: $font-secondary;
    font-size: $font-size-base;
    color: $color-text-secondary;
    text-align: center;
    margin-top: $spacing-lg;
    background: $color-bg-button-secondary;
    padding: $spacing-xs;
    border-radius: $border-radius-sm;
    // border: 1px solid rgba($color-text-muted, 0.3);
  }

  .addressActionButton {
    width: fit-content;
    padding: $spacing-sm $spacing-lg;
    background: $color-primary;
    color: $color-bg-white;
    border: none;
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-weight: $font-weight-semibold;
    font-size: $font-size-base;
    cursor: pointer;
    min-width: 150px;
  }

  .addressItem {
    display: flex;
    gap: $spacing-md;
    margin: $spacing-md 0px;

    .addressName {
      flex: 2;
    }

    .addressDetails {
      flex: 2;
    }

    .addressActions {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;
      align-items: end;
    }
  }
}

.saveBtn {
  grid-column: 1 / -1;
  padding: $spacing-sm $spacing-lg;
  background: $color-primary;
  color: $color-bg-white;
  border: none;
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-weight: $font-weight-semibold;
  font-size: $font-size-base;
  cursor: pointer;
  transition: background $transition-fast;
  margin-top: $spacing-md;

  &:hover {
    background: $color-primary-hover;
  }
}

.settingsSection {
  .settingItem {
    margin-bottom: $spacing-lg;
    padding: $spacing-md;
    background: rgba($color-primary, 0.05);
    border-radius: $border-radius-sm;
    border-left: 3px solid $color-primary;
  }
}

.switchLabel {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  font-family: $font-secondary;
  font-weight: $font-weight-medium;
  color: $color-text-dark;
  cursor: pointer;

  input[type="checkbox"] {
    display: none;
  }
}

.switch {
  position: relative;
  width: 50px;
  height: 24px;
  background: rgba($color-text-muted, 0.3);
  border-radius: 12px;
  transition: background $transition-fast;

  &::before {
    content: "";
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: $color-bg-white;
    border-radius: 50%;
    transition: transform $transition-fast;
  }

  input:checked + & {
    background: $color-primary;

    &::before {
      transform: translateX(26px);
    }
  }
}

.securitySection {
  .tokenInfo {
    margin-bottom: $spacing-xl;
    padding: $spacing-lg;
    background: rgba($color-secondary, 0.1);
    border-radius: $border-radius-sm;
    border: 1px solid rgba($color-secondary, 0.2);

    p {
      margin-bottom: $spacing-sm;
      color: $color-text-secondary;
      font-family: $font-secondary;

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        color: $color-text-dark;
      }

      code {
        display: block;
        margin-top: $spacing-xs;
        padding: $spacing-xs $spacing-sm;
        background: $color-bg-input;
        border-radius: $border-radius-sm;
        font-family: $font-secondary;
        font-size: $font-size-sm;
        word-break: break-all;
        color: $color-text-dark;
      }
    }
  }

  .passwordSection {
    margin-bottom: $spacing-xl;
    padding: $spacing-lg;
    background: rgba($color-primary, 0.05);
    border-radius: $border-radius-sm;
    border: 1px solid rgba($color-primary, 0.2);
  }

  .dangerZone {
    padding: $spacing-lg;
    background: rgba($color-danger, 0.05);
    border-radius: $border-radius-sm;
    border: 1px solid rgba($color-danger, 0.2);

    h3 {
      color: $color-danger;
      margin-bottom: $spacing-md;
    }
  }
}

.logoutBtn {
  padding: $spacing-sm $spacing-lg;
  background: $color-danger;
  color: $color-bg-white;
  border: none;
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-weight: $font-weight-semibold;
  font-size: $font-size-base;
  cursor: pointer;
  transition: background $transition-fast;

  &:hover {
    background: darken($color-danger, 10%);
  }
}
