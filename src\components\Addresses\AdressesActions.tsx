import { UMBRACO_URL } from "../../env-config";
import useAuth from "../Auth/AuthActions";
import type { AuthStatusProps } from "../Auth/AuthTypes";
import type { NotificationsProps } from "../Notifications/Notification";
import AddressItem from "./AddressItem";

interface AdressesProps extends NotificationsProps, AuthStatusProps {}

const useAddresses = ({
  setNotifications,
  notifications,
  setIsLoggedIn,
}: AdressesProps) => {
  const { getBearerToken } = useAuth({
    setNotifications,
    notifications,
    setIsLoggedIn,
  });
  const token = getBearerToken();

  const getAddresses = async () => {
    if (!token) throw new Error("Login failed");

    try {
      const response = await fetch(`${UMBRACO_URL}/Account/Addresses`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Login failed");
      }

      const data = await response.json();
      const addressesRaw = data.Addresses;
      const addresses: AddressItem[] = addressesRaw.map(
        (item: any) => new AddressItem(item)
      );

      return addresses;
    } catch (error) {
      throw error;
    }
  };

  return { getAddresses };
};

export default useAddresses;
