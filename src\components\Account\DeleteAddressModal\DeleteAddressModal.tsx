import React, { useState } from 'react';
import Modal from '../../Modal';
import styles from './DeleteAddressModal.module.scss';
import { UMBRACO_URL } from '../../../env-config';
import useAuth from '../../Auth/AuthActions';
import type { NotificationsProps } from '../../Notifications/Notification';
import type AddressItem from '../../Addresses/AddressItem';

interface DeleteAddressModalProps extends NotificationsProps {
  isOpen: boolean;
  onClose: () => void;
  onAddressDeleted: () => void;
  address: AddressItem | null;
}

const DeleteAddressModal: React.FC<DeleteAddressModalProps> = ({
  isOpen,
  onClose,
  onAddressDeleted,
  address,
  notifications,
  setNotifications,
}) => {
  const { getBearerToken } = useAuth({ notifications, setNotifications });
  const [isLoading, setIsLoading] = useState(false);

  const handleDelete = async () => {
    if (!address) return;
    
    setIsLoading(true);

    try {
      const token = await getBearerToken();
      if (!token) {
        throw new Error("Authentication required");
      }

      const response = await fetch(`${UMBRACO_URL}/Account/AddressDelete?addressId=${address.Id}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete address: ${errorText}`);
      }

      setNotifications([
        ...notifications,
        { id: Date.now(), message: 'Address deleted successfully!' }
      ]);
      
      onAddressDeleted();
      onClose();

    } catch (error) {
      console.error('Error deleting address:', error);
      setNotifications([
        ...notifications,
        { id: Date.now(), message: `Error deleting address: ${error}` }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  if (!address) return null;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Delete Address">
      <div className={styles.deleteContent}>
        <div className={styles.warningIcon}>⚠️</div>
        <h3>Are you sure you want to delete this address?</h3>
        
        <div className={styles.addressPreview}>
          <div className={styles.addressName}>
            <strong>{address.FirstName} {address.LastName}</strong>
          </div>
          <div className={styles.addressDetails}>
            <p>{address.Address1}</p>
            {address.Address2 && <p>{address.Address2}</p>}
            <p>{address.City}, {address.ZipPostalCode}</p>
            <p>{address.StateProvinceName}, {address.CountryName}</p>
          </div>
        </div>

        <p className={styles.warningText}>
          This action cannot be undone. The address will be permanently removed from your account.
        </p>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={handleClose}
            className={styles.cancelButton}
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleDelete}
            className={styles.deleteButton}
            disabled={isLoading}
          >
            {isLoading ? 'Deleting...' : 'Yes, Delete'}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteAddressModal;
