import React from "react";
import styles from "./ProfileTab.module.scss";
import type AddressItem from "../../Addresses/AddressItem";

interface ProfileTabProps {
  addresses: AddressItem[];
}

const ProfileTab: React.FC<ProfileTabProps> = ({ addresses }) => {
  return (
    <div className={styles.tabContent}>
      <h2>Profile Information</h2>
      <div className={styles.profileSection}>
        <div className={styles.nameSection}>
          <div className={styles.formGroup}>
            <label htmlFor="firstName">First Name</label>
            <input
              type="text"
              id="firstName"
              placeholder="Enter your first name"
            />
          </div>
          <div className={styles.formGroup}>
            <label htmlFor="lastName">Last Name</label>
            <input
              type="text"
              id="lastName"
              placeholder="Enter your last name"
            />
          </div>
        </div>
        <div className={styles.formGroup}>
          <label htmlFor="email">Email</label>
          <input type="email" id="email" readOnly />
        </div>
      </div>

      <h2>Addresses</h2>
      <div className={styles.profileSection}>
        <button className={styles.addressActionButton}>+ Add new</button>
        {addresses.length === 0 ? (
          <div className={styles.noAdreesses}>No addresses</div>
        ) : (
          <div>
            {addresses.map((address) => (
              <div key={address.Id} className={styles.addressItem}>
                <div className={styles.addressName}>
                  <h3>
                    {address.FirstName} {address.LastName}
                  </h3>
                  {address.Email}
                </div>
                <div className={styles.addressDetails}>
                  <p>{address.Address1}</p>
                  <p>
                    {address.StateProvinceName}, {address.ZipPostalCode}{" "}
                    {address.City}
                  </p>
                  <p>{address.CountryName}</p>
                  <p>
                    <strong>Phone number: </strong>
                    {address.PhoneNumber}
                  </p>
                </div>
                <div className={styles.addressActions}>
                  <button className={styles.addressActionButton}>Edit</button>
                  <button className={styles.addressActionButton}>Delete</button>
                </div>
              </div>
            ))}
          </div>
        )}
        <button className={styles.saveBtn}>Save Changes</button>
      </div>
    </div>
  );
};

export default ProfileTab;
