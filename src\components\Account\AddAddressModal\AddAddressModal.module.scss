@import "../../../styles/_variables.scss";

.addressForm {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.formRow {
  display: flex;
  gap: $spacing-md;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: $spacing-sm;
  }
}

.formGroup {
  flex: 1;
  display: flex;
  flex-direction: column;

  label {
    margin-bottom: $spacing-xs;
    font-family: $font-secondary;
    font-weight: $font-weight-medium;
    color: $color-text-dark;
    font-size: $font-size-sm;
  }

  input {
    width: 100%;
    padding: $spacing-sm $spacing-md;
    border: 1px solid rgba($color-text-muted, 0.3);
    border-radius: $border-radius-sm;
    font-family: $font-secondary;
    font-size: $font-size-base;
    background: $color-bg-white;
    transition: border-color $transition-fast;

    &:focus {
      outline: none;
      border-color: $color-primary;
      box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
    }

    &:disabled {
      background: rgba($color-text-muted, 0.1);
      cursor: not-allowed;
    }

    &::placeholder {
      color: $color-text-muted;
    }
  }
}

.formActions {
  display: flex;
  gap: $spacing-md;
  justify-content: flex-end;
  margin-top: $spacing-lg;
  padding-top: $spacing-lg;
  border-top: 1px solid rgba($color-text-muted, 0.2);

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.cancelButton {
  padding: $spacing-sm $spacing-lg;
  background: transparent;
  color: $color-text-secondary;
  border: 1px solid rgba($color-text-muted, 0.3);
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-weight: $font-weight-medium;
  font-size: $font-size-base;
  cursor: pointer;
  transition: all $transition-fast;
  min-width: 120px;

  &:hover:not(:disabled) {
    background: rgba($color-text-muted, 0.1);
    border-color: $color-text-secondary;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.saveButton {
  padding: $spacing-sm $spacing-lg;
  background: $color-primary;
  color: $color-bg-white;
  border: none;
  border-radius: $border-radius-sm;
  font-family: $font-secondary;
  font-weight: $font-weight-semibold;
  font-size: $font-size-base;
  cursor: pointer;
  transition: background $transition-fast;
  min-width: 120px;

  &:hover:not(:disabled) {
    background: $color-primary-hover;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}
